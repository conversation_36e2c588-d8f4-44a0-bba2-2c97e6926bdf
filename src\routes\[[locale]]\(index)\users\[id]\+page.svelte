<script lang="ts">
  import type { Localization } from "$lib";

  import { onMount } from "svelte";
  import { findLocalizationForUserLocales, fetchWithAuth } from "$lib";
  import DetailImageCarousel from "./detail-image-carousel.svelte";
  import { page } from "$app/state";

  interface UserImage {
    id: string;
    url: string;
    source: string;
  }

  interface User {
    id: string;
    email: string;
    role: "user" | "admin" | "moderator";
    name: Localization[];
    description: Localization[];
    images?: UserImage[];
    createdAt: string;
    updatedAt: string;
  }

  const i18n = {
    en: {
      _page: {
        title: "— Commune",
      },
      loading: "Loading...",
      userNotFound: "User not found",
      noDescription: "No description available",
      userDetails: "User Details",
      joinedOn: "Joined on",
      errorFetchingUser: "Failed to fetch user",
      errorOccurred: "An error occurred while fetching user",
      dateFormatLocale: "en-US",
    },
    ru: {
      _page: {
        title: "— Коммуна",
      },
      loading: "Загрузка...",
      userNotFound: "Пользователь не найден",
      noDescription: "Нет описания",
      userDetails: "Информация о пользователе",
      joinedOn: "Дата регистрации",
      errorFetchingUser: "Не удалось загрузить пользователя",
      errorOccurred: "Произошла ошибка при загрузке пользователя",
      dateFormatLocale: "ru-RU",
    },
  };

  const { data } = $props();
  const { locale, routeLocale } = $derived(data);

  const userId = page.params.id;

  const t = $derived(i18n[locale]);

  // State using runes
  let user = $state<User | null>(null);
  let loading = $state(true);
  let error = $state<string | null>(null);

  // Function to fetch user data
  async function fetchUser() {
    loading = true;

    try {
      const response = await fetchWithAuth(`/api/user/${userId}`);

      if (!response.ok) {
        throw new Error(`${t.errorFetchingUser}: ${response.statusText}`);
      }

      const data = await response.json();
      user = data;
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      loading = false;
    }
  }

  // Derived values
  const userName = $derived(user ? findLocalizationForUserLocales(user.name, routeLocale) : "");
  const userDescription = $derived(
    user ? findLocalizationForUserLocales(user.description, routeLocale) : "",
  );

  const joinDate = $derived(user ? new Date(user.createdAt) : new Date());
  const formattedDate = $derived(
    joinDate.toLocaleDateString(t.dateFormatLocale, {
      year: "numeric",
      month: "long",
      day: "numeric",
    }),
  );

  // Get badge class based on user role
  const getBadgeClass = (role: string) => {
    switch (role) {
      case "admin":
        return "bg-danger";
      case "moderator":
        return "bg-warning";
      default:
        return "bg-primary";
    }
  };

  onMount(() => {
    fetchUser();
  });
</script>

<svelte:head>
  <title>{userName} {t._page.title}</title>
</svelte:head>

<div class="container py-4">
  {#if loading}
    <div class="text-center py-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">{t.loading}</span>
      </div>
    </div>
  {:else if error || !user}
    <div class="alert alert-danger" role="alert">
      {error || t.userNotFound}
    </div>
  {:else}
    <div class="row">
      <div class="col-lg-8">
        <!-- Image Carousel -->
        <DetailImageCarousel images={user.images || []} {locale} />

        <!-- User Information -->
        <div class="mb-4">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h2 class="mb-0">{userName}</h2>
            <span class={`badge ${getBadgeClass(user.role)}`}>
              {user.role}
            </span>
          </div>
          <p class="lead text-muted">{userDescription || t.noDescription}</p>
        </div>
      </div>

      <div class="col-lg-4">
        <div class="card shadow-sm mb-4">
          <div class="card-body">
            <h5 class="card-title">{t.userDetails}</h5>
            <hr />
            <div class="d-flex align-items-center mb-3">
              <i class="bi bi-envelope-fill me-2 text-primary"></i>
              <span>{user.email}</span>
            </div>
            <div class="d-flex align-items-center">
              <i class="bi bi-calendar-date me-2 text-primary"></i>
              <span>{t.joinedOn} {formattedDate}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  {/if}
</div>
